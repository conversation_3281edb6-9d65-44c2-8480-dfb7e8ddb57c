// Script de prueba para verificar la validación del chat
const { ApiAIInputSchema } = require('./src/lib/zodSchemas.ts');

// Simular datos de chat como los que envía el frontend
const testChatData = {
  pregunta: "¿Cuál es el contenido principal de este documento?",
  documentos: [
    {
      id: "test-doc-1",
      titulo: "Documento de prueba",
      categoria: "General",
      numero_tema: 1,
      storage_path: "documents/test-file.txt",
      // Nota: NO incluimos 'contenido' porque viene de Storage
    }
  ]
};

console.log('Probando validación de datos de chat...');
console.log('Datos de prueba:', JSON.stringify(testChatData, null, 2));

try {
  const result = ApiAIInputSchema.parse(testChatData);
  console.log('✅ Validación exitosa!');
  console.log('Resultado:', result);
} catch (error) {
  console.log('❌ Error de validación:');
  console.log(error.errors || error.message);
}
