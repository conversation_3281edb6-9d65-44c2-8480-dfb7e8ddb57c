// Script para probar la validación del esquema
const testData = {
  pregunta: "apartados del tema?",
  documentos: [
    {
      id: "add57a10-a9ff-4dd1-ace0-1d00a91146f7",
      titulo: "Tema 04.pdf nuevo temario",
      storage_path: "documents/add57a10-a9ff-4dd1-ace0-1d00a91146f7.txt",
      categoria: "General",
      numero_tema: 4,
      // Campos que podrían estar faltando:
      creado_en: "2024-01-01T00:00:00.000Z",
      actualizado_en: "2024-01-01T00:00:00.000Z",
      user_id: "test-user-id"
    }
  ]
};

console.log('Datos de prueba para validación:');
console.log(JSON.stringify(testData, null, 2));

// Para usar en la consola del navegador:
// 1. Copia este objeto
// 2. Importa el esquema: import { ApiAIInputSchema } from './src/lib/zodSchemas.ts'
// 3. Ejecuta: ApiAIInputSchema.safeParse(testData)
